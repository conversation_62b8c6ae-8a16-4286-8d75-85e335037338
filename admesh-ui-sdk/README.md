# AdMesh UI SDK

A React + TypeScript component library for displaying AdMesh product recommendations with built-in tracking and theming support.

## 🚀 Features

- **Pre-built UI Components** - Ready-to-use components for product recommendations
- **Built-in Tracking** - Automatic click, view, and conversion tracking
- **Intelligent Layouts** - Auto-selects optimal layout based on intent and data
- **Theme Support** - Light/dark mode with custom accent colors
- **TypeScript First** - Full type safety and IntelliSense support
- **Framework Agnostic** - React core, but designed for easy embedding
- **Responsive Design** - Mobile-first responsive components
- **Accessibility** - WCAG 2.1 AA compliant

## 📦 Installation

```bash
npm install admesh-ui-sdk
```

## 🎯 Quick Start

```tsx
import React from 'react';
import { AdMeshLayout } from 'admesh-ui-sdk';
// No CSS import needed! Styles are auto-injected ✨

const recommendations = [
  {
    title: "HubSpot CRM",
    reason: "Perfect for remote teams with excellent collaboration features",
    intent_match_score: 0.92,
    admesh_link: "https://useadmesh.com/track?ad_id=hubspot-123",
    ad_id: "hubspot-123",
    product_id: "hubspot-crm",
    has_free_tier: true,
    trial_days: 14,
    keywords: ["CRM", "Sales", "Marketing"]
  }
];

function App() {
  return (
    <AdMeshLayout
      recommendations={recommendations}
      autoLayout={true}
      showMatchScores={true}
      onProductClick={(adId, admeshLink) => {
        console.log('Product clicked:', { adId, admeshLink });
      }}
    />
  );
}
```

## 🧩 Components

### Core Components

#### AdMeshLayout
The main layout component that automatically chooses the best display format.

#### AdMeshProductCard
Individual product recommendation card.

#### AdMeshCompareTable
Side-by-side product comparison table.

#### AdMeshBadge
Reusable badge component.

#### AdMeshLinkTracker
Wrapper for tracking any clickable element.

### 💬 Conversational Ad Units

#### AdMeshConversationalUnit
Smart conversational ad component that adapts to different chat contexts and display modes.

#### AdMeshConversationSummary
End-of-conversation summary with top recommendations and action buttons.

#### AdMeshInlineRecommendation
Compact inline recommendation component perfect for chat interfaces.

### 📋 Sidebar Components

#### AdMeshSidebar
Persistent sidebar component for displaying recommendations alongside your main content.

#### AdMeshSidebarHeader
Customizable header with search, title, and collapse functionality.

#### AdMeshSidebarContent
Content area with tabs, filtering, and multiple display modes.

### 💬 Chat Components

#### AdMeshFloatingChat
Floating chat widget that can be embedded on any website for AI-powered recommendations.

#### AdMeshChatInterface
Embeddable chat interface for integrating conversational AI into web applications.

#### AdMeshChatMessage
Individual chat message component with recommendation support.

#### AdMeshChatInput
Chat input component with suggestions and auto-resize functionality.

## 🎨 Theming

```tsx
const theme = {
  mode: 'dark', // 'light' | 'dark'
  accentColor: '#8b5cf6', // Custom accent color
};
```

## 💬 Conversational Ad Units

Perfect for chat interfaces, AI assistants, and conversational experiences.

### Quick Start - Conversational Units

```tsx
import React from 'react';
import { AdMeshConversationalUnit } from 'admesh-ui-sdk';

const recommendations = [
  {
    title: "HubSpot CRM",
    reason: "Perfect for remote teams with excellent collaboration features",
    intent_match_score: 0.92,
    admesh_link: "https://useadmesh.com/track?ad_id=hubspot-123",
    ad_id: "hubspot-123",
    product_id: "hubspot-crm",
    has_free_tier: true,
    trial_days: 14,
    keywords: ["CRM", "Sales", "Marketing"]
  }
];

function ChatInterface() {
  return (
    <div className="chat-container">
      {/* Your chat messages */}
      <div className="message">I need a CRM for my team</div>

      {/* AdMesh conversational ad unit */}
      <AdMeshConversationalUnit
        recommendations={recommendations}
        config={{
          displayMode: 'inline', // 'inline' | 'minimal' | 'floating' | 'summary'
          context: 'chat',
          maxRecommendations: 3,
          showPoweredBy: true
        }}
        onRecommendationClick={(adId, admeshLink) => {
          window.open(admeshLink, '_blank');
        }}
      />
    </div>
  );
}
```

### Display Modes

#### Inline Mode
Full recommendations displayed inline with the conversation:
```tsx
<AdMeshConversationalUnit
  recommendations={recommendations}
  config={{ displayMode: 'inline', context: 'chat' }}
/>
```

#### Minimal Mode
Compact display showing match count and top recommendation:
```tsx
<AdMeshConversationalUnit
  recommendations={recommendations}
  config={{ displayMode: 'minimal', context: 'assistant' }}
/>
```

#### Floating Mode
Floating overlay that doesn't interrupt the conversation flow:
```tsx
<AdMeshConversationalUnit
  recommendations={recommendations}
  config={{ displayMode: 'floating', context: 'support' }}
/>
```

#### Summary Mode
End-of-conversation summary with top recommendations:
```tsx
<AdMeshConversationalUnit
  recommendations={recommendations}
  config={{ displayMode: 'summary', context: 'agent' }}
  conversationSummary="We discussed your CRM needs..."
/>
```

### Individual Conversational Components

#### AdMeshConversationSummary
Perfect for end-of-conversation summaries:

```tsx
import { AdMeshConversationSummary } from 'admesh-ui-sdk';

<AdMeshConversationSummary
  recommendations={recommendations}
  conversationSummary="Here's what we discussed and found for you..."
  showTopRecommendations={3}
  onRecommendationClick={(adId, link) => window.open(link)}
  onStartNewConversation={() => startNewChat()}
/>
```

#### AdMeshInlineRecommendation
Compact inline recommendations for chat bubbles:

```tsx
import { AdMeshInlineRecommendation } from 'admesh-ui-sdk';

<AdMeshInlineRecommendation
  recommendation={recommendation}
  compact={true}
  showReason={true}
  onClick={(adId, link) => window.open(link)}
/>
```

### Configuration Options

#### ConversationalAdConfig

```tsx
interface ConversationalAdConfig {
  displayMode: 'inline' | 'summary' | 'floating' | 'minimal';
  context: 'chat' | 'assistant' | 'agent' | 'support';
  maxRecommendations?: number;     // Default: 3
  showPoweredBy?: boolean;         // Default: true
  autoShow?: boolean;              // Default: true
  delayMs?: number;                // Default: 0
  position?: 'top' | 'bottom' | 'inline'; // Default: 'inline'
}
```

### Integration Examples

#### Chat Application
```tsx
function ChatApp() {
  const [messages, setMessages] = useState([]);
  const [recommendations, setRecommendations] = useState([]);

  const handleUserMessage = async (message) => {
    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: message }]);

    // Get AI response and recommendations
    const response = await getAIResponse(message);
    setMessages(prev => [...prev, { role: 'assistant', content: response.text }]);

    // Show recommendations if available
    if (response.recommendations) {
      setRecommendations(response.recommendations);
    }
  };

  return (
    <div className="chat-container">
      {messages.map((msg, i) => (
        <div key={i} className={`message ${msg.role}`}>
          {msg.content}

          {/* Show recommendations after assistant messages */}
          {msg.role === 'assistant' && recommendations.length > 0 && (
            <AdMeshConversationalUnit
              recommendations={recommendations}
              config={{
                displayMode: 'inline',
                context: 'chat',
                maxRecommendations: 2
              }}
            />
          )}
        </div>
      ))}
    </div>
  );
}

<AdMeshLayout theme={theme} recommendations={recommendations} />
```

## 📋 Sidebar Components

Perfect for applications that need persistent recommendation panels alongside main content.

### Quick Start - Sidebar

```tsx
import React, { useState } from 'react';
import { AdMeshSidebar } from 'admesh-ui-sdk';

function AppWithSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* AdMesh Sidebar */}
      <AdMeshSidebar
        recommendations={recommendations}
        config={{
          position: 'left',        // 'left' | 'right'
          size: 'md',             // 'sm' | 'md' | 'lg' | 'xl'
          displayMode: 'recommendations', // 'recommendations' | 'mixed' | 'history' | 'favorites'
          collapsible: true,
          showHeader: true,
          showSearch: true,
          maxRecommendations: 10
        }}
        theme={{ mode: 'light' }}
        title="AI Recommendations"
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
        onRecommendationClick={(adId, admeshLink) => {
          window.open(admeshLink, '_blank');
        }}
      />

      {/* Your main content */}
      <div className={`transition-all duration-300 ${sidebarOpen ? 'ml-80' : 'ml-0'}`}>
        <div className="p-8">
          <h1>Your Application Content</h1>
          <p>The sidebar appears alongside your main content.</p>
        </div>
      </div>
    </div>
  );
}
```

### Sidebar Features

- **Multiple Positions**: Left or right sidebar placement
- **Responsive Sizes**: Small to extra-large width options
- **Display Modes**: Recommendations, mixed, history, and favorites views
- **Search & Filter**: Built-in search and filtering capabilities
- **Collapsible**: Space-saving collapsed state
- **Tabs**: All, Top, and Recent recommendation tabs
- **Theme Support**: Light/dark mode with custom accent colors

### Configuration Examples

```tsx
// Compact right sidebar
<AdMeshSidebar
  config={{
    position: 'right',
    size: 'sm',
    displayMode: 'recommendations',
    showSearch: false,
    maxRecommendations: 5
  }}
/>

// Large sidebar with mixed display
<AdMeshSidebar
  config={{
    position: 'left',
    size: 'lg',
    displayMode: 'mixed',
    collapsible: true,
    showSearch: true,
    showFilters: true
  }}
/>
```

## 💬 Chat Components

Perfect for websites and applications that want to provide AI-powered recommendations through chat interfaces.

### Quick Start - Floating Chat Widget

```tsx
import React, { useState } from 'react';
import { AdMeshFloatingChat } from 'admesh-ui-sdk';

function WebsiteWithChat() {
  const [chatOpen, setChatOpen] = useState(false);

  const handleSendMessage = async (message) => {
    // Call your AI API to get response with recommendations
    const response = await fetch('/api/chat', {
      method: 'POST',
      body: JSON.stringify({ message })
    });

    const data = await response.json();

    return {
      id: `assistant-${Date.now()}`,
      role: 'assistant',
      content: data.response,
      timestamp: new Date(),
      recommendations: data.recommendations
    };
  };

  return (
    <div>
      {/* Your website content */}
      <main>
        <h1>Your Website</h1>
        <p>The chat widget appears in the corner</p>
      </main>

      {/* AdMesh Floating Chat */}
      <AdMeshFloatingChat
        config={{
          position: 'bottom-right',    // 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
          size: 'md',                 // 'sm' | 'md' | 'lg' | 'xl'
          autoOpen: false,            // Auto-open on page load
          showWelcomeMessage: true,
          welcomeMessage: "Hi! How can I help you find the perfect products?",
          enableSuggestions: true,
          suggestions: [
            "I need software recommendations",
            "What tools do you recommend?",
            "Help me find the right solution"
          ]
        }}
        theme={{ mode: 'light' }}
        title="AI Assistant"
        subtitle="Get personalized recommendations"
        isOpen={chatOpen}
        onToggle={() => setChatOpen(!chatOpen)}
        onSendMessage={handleSendMessage}
        onRecommendationClick={(adId, admeshLink) => {
          window.open(admeshLink, '_blank');
        }}
      />
    </div>
  );
}
```

### Embedded Chat Interface

```tsx
import React, { useState } from 'react';
import { AdMeshChatInterface } from 'admesh-ui-sdk';

function ChatPage() {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async (messageContent) => {
    // Add user message
    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: messageContent,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Get AI response
    const response = await getAIResponse(messageContent);
    setMessages(prev => [...prev, response]);
    setIsLoading(false);
  };

  return (
    <div className="h-screen">
      <AdMeshChatInterface
        messages={messages}
        config={{
          placeholder: "Ask me about products...",
          enableTypingIndicator: true,
          maxMessages: 50
        }}
        theme={{ mode: 'light' }}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
        onRecommendationClick={(adId, link) => window.open(link)}
      />
    </div>
  );
}
```

### Chat Features

- **Floating Widget**: Non-intrusive chat button that expands to full chat
- **Multiple Positions**: Place in any corner of the screen
- **Responsive Sizes**: From compact mobile to large desktop
- **Auto-open**: Proactive engagement with visitors
- **Welcome Messages**: Customizable greeting messages
- **Quick Suggestions**: Pre-defined conversation starters
- **Typing Indicators**: Visual feedback during AI response generation
- **Message History**: Persistent conversation state
- **Recommendation Display**: Inline product recommendations with tracking
- **Theme Support**: Light/dark modes with custom branding

### Configuration Examples

```tsx
// Compact bottom-left widget
<AdMeshFloatingChat
  config={{
    position: 'bottom-left',
    size: 'sm',
    autoOpen: false,
    showWelcomeMessage: true,
    welcomeMessage: "Need help finding something?"
  }}
/>

// Large auto-opening widget with suggestions
<AdMeshFloatingChat
  config={{
    position: 'bottom-right',
    size: 'lg',
    autoOpen: true,
    enableSuggestions: true,
    suggestions: [
      "Show me your best products",
      "I need business software",
      "Help me choose the right tool"
    ]
  }}
/>

// Embedded chat with message limit
<AdMeshChatInterface
  config={{
    maxMessages: 20,
    enableTypingIndicator: true,
    placeholder: "What can I help you find today?"
  }}
/>
```

## 📊 Tracking

All components include built-in view and click tracking:

```tsx
import { setAdMeshTrackerConfig } from '@admesh/ui-sdk';

setAdMeshTrackerConfig({
  apiBaseUrl: 'https://api.useadmesh.com',
  enabled: true,
  debug: true
});
```

## 🔗 Integration with AdMesh SDKs

Works seamlessly with AdMesh backend SDKs:

```tsx
import { AdMesh } from '@admesh/typescript-sdk';
import { AdMeshLayout } from '@admesh/ui-sdk';

const client = new AdMesh({ apiKey: 'your-api-key' });

async function getRecommendations(query: string) {
  const response = await client.recommend.getRecommendations({
    query,
    format: 'auto'
  });

  return (
    <AdMeshLayout
      recommendations={response.response?.recommendations || []}
      autoLayout={true}
    />
  );
}
```

## 📚 API Reference

### Types

```tsx
interface AdMeshRecommendation {
  title: string;
  reason: string;
  intent_match_score: number; // 0-1
  admesh_link: string;
  ad_id: string;
  product_id: string;
  features?: string[];
  has_free_tier?: boolean;
  trial_days?: number;
  pricing?: string;
  keywords?: string[];
}

type IntentType =
  | 'compare_products'
  | 'best_for_use_case'
  | 'trial_demo'
  | 'budget_conscious';

interface AdMeshTheme {
  mode: 'light' | 'dark';
  accentColor?: string;
}
```

## 🛠 Development

```bash
# Install dependencies
npm install

# Start Storybook
npm run storybook

# Build library
npm run build

# Run linting
npm run lint
```

## 📄 License

MIT License

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and stories
5. Submit a pull request

## 📞 Support

- Documentation: [AdMesh Docs](https://docs.useadmesh.com)
- GitHub Issues: [Report a bug](https://github.com/GouniManikumar12/admesh-ui-sdk/issues)
- Email: <EMAIL>
