"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { centsToDollars, formatCurrency } from "@/lib/utils";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/use-auth";
import OfferActivationDialog from "@/components/OfferActivationDialog";
import {
  Bar<PERSON>hart,
  ArrowUpRight,
  MousePointerClick,
  Clock,
  // Filter, // Uncomment when needed
} from "lucide-react";

export default function OffersPage() {
  const { user } = useAuth();
  const router = useRouter();
  interface Offer {
    id: string;
    title: string;
    status: string;
    clicks: number;
    conversions: number;
    conversions_production?: number; // Added for production conversions
    budget: number;
    offer_total_budget_allocated: number;
    total_spent_offer: number;
    total_spent_production?: number; // Added for production spending
    total_spent_test?: number; // Added for test spending
    view_count?: number; // Added for product views
    ctr: string | number;
    reward: string;
    updatedAt: string;
    description: string;
    url: string;
    keywords: string[];
    categories: string[];
  }

  interface OfferApiResponse {
    id: string;
    offer_id?: string; // Sometimes returned from backend
    title: string;
    active: boolean;
    brand_id: string;
    product_id: string;

    // Click tracking
    clicks?: number; // Added for backward compatibility
    click_count: number | {
      test: number;
      total: number;
      production?: number;
    };
    clicks_production?: number; // Sometimes returned directly
    clicks_test?: number; // Sometimes returned directly

    // Conversion tracking
    conversions?: number; // Added for backward compatibility
    conversion_count: number | {
      production: number;
      test: number;
      total: number;
    };
    conversions_test?: number;
    conversions_production?: number;
    conversions_total?: number;

    // Financial data
    budget: number;
    offer_total_budget_allocated?: number;
    offer_total_budget_spent?: number;
    offer_total_promo_spent?: number;
    offer_total_promo_available?: number;
    total_spent_offer: number;
    total_spent?: number | {
      production: number;
      test: number;
      all: number;
    };

    // Offer details
    view_count?: number; // Added for product views
    reward_note: string;
    payout: {
      amount: number;
      currency: string;
      model?: string; // CPA, CPL, CPI, RevShare
    } | null;
    goal?: string; // signup, purchase, lead, app_install, click

    // Tracking and integration
    tracking?: {
      method?: string; // redirect_pixel, server_api, manual
      webhook_url?: string;
      notes?: string;
      redirect_url?: string;
      target_urls?: string[];
    };

    // Timestamps and metadata
    created_at?: string | null;
    last_converted_at: string | null;
    updated_at?: string | null;

    // Content
    description: string;
    url: string;
    keywords: string[];
    categories: string[];

    // Additional fields
    trust_score?: number;
    suggestion_reason?: string;
    valid_until?: string | null;
    meta?: Record<string, unknown>;
    ctr?: number;
    spent?: number; // Sometimes returned directly
  }

  interface ApiTotals {
    clicks: number; // Total clicks (production + test)
    clicks_production?: number; // Production clicks only
    clicks_test?: number; // Test clicks only
    conversions: number; // Production conversions
    conversions_test?: number; // Test conversions
    conversions_all?: number; // Total conversions (production + test)
    budget: number;
    total_spent_offer: number;
    total_spent?: {
      production: number;
      test: number;
      all: number;
    };
    view_count?: number; // Combined product views + offer views
    product_views?: number; // Product views only
    offer_views?: {
      total: number;
      production: number;
      test: number;
    }; // Offer views
    ctr: number;
  }

  const [offers, setOffers] = useState<Offer[]>([]);
  const [totals, setTotals] = useState<ApiTotals>({
    clicks: 0,
    clicks_production: 0,
    clicks_test: 0,
    conversions: 0,
    conversions_test: 0,
    conversions_all: 0,
    budget: 0,
    total_spent_offer: 0,
    total_spent: {
      production: 0,
      test: 0,
      all: 0
    },
    view_count: 0,
    product_views: 0,
    offer_views: {
      total: 0,
      production: 0,
      test: 0
    },
    ctr: 0,
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  const [detailedOffer, setDetailedOffer] = useState<OfferApiResponse | null>(null);

  useEffect(() => {
    if (!user) return;

    const fetchOffers = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();

        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) {
          throw new Error("Failed to fetch offers");
        }

        const data = await res.json();
        console.log(data);

        // Set totals from API response if available
        if (data.totals) {
          setTotals(data.totals);
        }

        // Log the data to see what we're getting
        console.log('API response totals:', data.totals);
        console.log('API response offers:', data.offers);

        // Check if there are any active offers
        const activeOffers = data.offers.filter((offer: OfferApiResponse) => offer.active);
        setHasActiveOffer(activeOffers.length > 0);

        const results = data.offers.map((offer: OfferApiResponse) => {
          // Extract production click count from either the new object structure or fallback to old format
          // Explicitly use only production clicks, not test clicks or total clicks
          const clickCount = typeof offer.click_count === 'object'
            ? (offer.click_count.production || 0)
            : (offer.clicks || offer.click_count || 0);

          // Extract production conversion count from either the new object structure or fallback to old format
          // Explicitly use only production conversions, not test conversions
          const conversionCount = typeof offer.conversion_count === 'object'
            ? offer.conversion_count.production || 0
            : (offer.conversions_production || 0);

          return {
            id: offer.id,
            title: offer.title || "Untitled",
            status: offer.active ? "Active" : "Inactive",
            clicks: clickCount,
            // Only use production conversions
            conversions: conversionCount,
            conversions_production: conversionCount,
            // Budget is stored in cents in the database, convert to dollars for display
            budget: offer.budget ? centsToDollars(offer.budget) : 0,
            // Get offer_total_budget_allocated
            offer_total_budget_allocated: offer.offer_total_budget_allocated ? centsToDollars(offer.offer_total_budget_allocated) : 0,
            // Only use production spending, convert from cents to dollars
            total_spent_offer: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent.production || 0) :
              centsToDollars(offer.total_spent_offer || 0),
            // Add production and test spending if available, convert from cents to dollars
            total_spent_production: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent?.production || 0) : undefined,
            total_spent_test: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent?.test || 0) : undefined,
            // Include view_count from the API response
            view_count: offer.view_count || 0,
            // Calculate CTR using only production conversions and production clicks
            // If there are no production clicks, CTR is 0
            ctr: clickCount > 0 && conversionCount >= 0
              ? ((conversionCount / clickCount) * 100).toFixed(1)
              : 0,
            reward:
              offer.reward_note ||
              (offer.payout?.amount
                ? formatCurrency(centsToDollars(offer.payout.amount), offer.payout.currency ?? "USD")
              : "—"),
            updatedAt: offer.last_converted_at
              ? format(new Date(offer.last_converted_at), "MMM dd, yyyy")
              : "—",
            description: offer.description || "",
            url: offer.url || "",
            keywords: offer.keywords || [],
            categories: offer.categories || [],
          };
        });

        setOffers(results);
      } catch (err) {
        console.error("Error fetching offers:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, [user]);

  const activeOffers = offers.filter((offer) => offer.status === "Active");
  console.log("activeOffers:", activeOffers);
  const inactiveOffers = offers.filter((offer) => offer.status === "Inactive");

  const displayOffers =
    activeTab === "active"
      ? activeOffers
      : activeTab === "inactive"
      ? inactiveOffers
      : offers;

  // Use totals from API or calculate if not available
  // Make sure we only count production clicks, not test clicks
  const totalClicks =
    totals.clicks_production || offers.reduce((sum, offer) => sum + offer.clicks, 0);

  // Only use production conversions
  const totalConversions =
    totals.conversions ||
    offers.reduce((sum, offer) => sum + (offer.conversions_production || 0), 0);

  // We no longer need budget and spent calculations since we removed those cards

  // No longer need budget usage percentage since we removed the budget cards

  return (
    <div className="p-6 space-y-6">
      {/* Header with Stats */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Promotional Offers
            </h2>
            <p className="text-muted-foreground text-sm">
              Manage and optimize your affiliate campaigns
            </p>
          </div>
          <Button
            onClick={() => {
              if (hasActiveOffer) {
                toast.error("You can only have one active offer at a time. Please deactivate your existing offer before creating a new one.");
              } else {
                router.push("/dashboard/brand/offers/new");
              }
            }}
          >
            + Create Offer
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Budget
                  </p>
                  <h3 className="text-2xl font-bold">
                    ${offers.reduce((sum, offer) => sum + offer.offer_total_budget_allocated, 0).toFixed(2)}
                  </h3>
                  <div className="text-xs text-muted-foreground mt-1">
                    Allocated across all offers
                  </div>
                </div>
                <div className="bg-indigo-100 p-2 rounded-full">
                  <BarChart className="text-indigo-600 h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Spent
                  </p>
                  <h3 className="text-2xl font-bold">
                    ${offers.reduce((sum, offer) => sum + offer.total_spent_offer, 0).toFixed(2)}
                  </h3>
                  <div className="text-xs text-muted-foreground mt-1">
                    Production conversions only
                  </div>
                </div>
                <div className="bg-red-100 p-2 rounded-full">
                  <ArrowUpRight className="text-red-600 h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Views
                  </p>
                  <h3 className="text-2xl font-bold">{totals.view_count || 0}</h3>
                  <div className="flex flex-col mt-1">
                    <span className="text-xs text-muted-foreground">
                      {totals.product_views || 0} product + {totals.offer_views?.production || 0} offer
                    </span>
                  </div>
                </div>
                <div className="bg-purple-100 p-2 rounded-full">
                  <BarChart className="text-purple-600 h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card> */}

          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Clicks
                  </p>
                  <h3 className="text-2xl font-bold">{totalClicks}</h3>
                  <div className="text-xs text-muted-foreground mt-1">
                    Production clicks only
                  </div>
                </div>
                <div className="bg-blue-100 p-2 rounded-full">
                  <MousePointerClick className="text-blue-600 h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Conversions
                  </p>
                  <h3 className="text-2xl font-bold">{totalConversions}</h3>
                  <div className="text-xs text-muted-foreground mt-1">
                    Production conversions only
                  </div>
                </div>
                <div className="bg-green-100 p-2 rounded-full">
                  <ArrowUpRight className="text-green-600 h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Offer Cards View (Alternative) */}
      {/* <div className="mt-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Featured Offers</h3>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {displayOffers.slice(0, 3).map((offer) => (
            <Card
              key={`card-${offer.id}`}
              className="overflow-hidden hover:shadow-md transition cursor-pointer"
              onClick={() =>
                router.push(`/dashboard/brand/offers/${offer.id}/analytics`)
              }
            >
              <div
                className={`h-1 w-full ${
                  offer.status === "Active" ? "bg-green-500" : "bg-gray-300"
                }`}
              ></div>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="font-semibold text-base">{offer.title}</h4>
                  <Badge
                    variant="outline"
                    className={
                      offer.status === "Active"
                        ? "bg-green-100 text-green-700 border-green-200"
                        : "bg-gray-100 text-gray-600 border-gray-200"
                    }
                  >
                    {offer.status}
                  </Badge>
                </div>

                <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
                  {offer.description || "No description provided"}
                </p>

                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="bg-blue-50 p-2 rounded">
                    <p className="text-xs text-muted-foreground">Clicks</p>
                    <p className="font-semibold">{offer.clicks}</p>
                  </div>
                  <div className="bg-green-50 p-2 rounded">
                    <p className="text-xs text-muted-foreground">Conversions</p>
                    <p className="font-semibold">{offer.conversions}</p>
                  </div>
                  <div className="bg-purple-50 p-2 rounded">
                    <p className="text-xs text-muted-foreground">Reward</p>
                    <p className="font-semibold">{offer.reward}</p>
                  </div>
                  <div className="bg-amber-50 p-2 rounded">
                    <p className="text-xs text-muted-foreground">CTR</p>
                    <p className="font-semibold">{offer.ctr}%</p>
                  </div>
                </div>

                <div className="flex justify-end mt-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/dashboard/brand/offers/${offer.id}/edit`);
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    variant={
                      offer.status === "Active" ? "destructive" : "default"
                    }
                    size="sm"
                    className="text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedOffer(offer);
                      setShowActivationDialog(true);
                    }}
                  >
                    {offer.status === "Active" ? "Pause" : "Activate"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {displayOffers.length > 3 && (
          <div className="flex justify-center mt-4">
            <Button variant="outline">
              View All {displayOffers.length} Offers
            </Button>
          </div>
        )}
      </div> */}
      {/* Tabs and Offers Table */}
      <Card className="shadow-sm border">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-semibold text-gray-900">
              Offers Overview
            </CardTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all">All Offers</TabsTrigger>
                <TabsTrigger value="active">
                  Active ({activeOffers.length})
                </TabsTrigger>
                <TabsTrigger value="inactive">
                  Inactive ({inactiveOffers.length})
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Reward</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Spent</TableHead>
                <TableHead>Clicks</TableHead>
                <TableHead>Conversions</TableHead>
                <TableHead>CTR</TableHead>
                <TableHead>Last Converted</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell
                    colSpan={10}
                    className="text-center py-6 text-sm text-muted-foreground"
                  >
                    Loading offers...
                  </TableCell>
                </TableRow>
              ) : displayOffers.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={10}
                    className="text-center py-10 text-sm text-muted-foreground"
                  >
                    {activeTab === "all"
                      ? "No offers yet. Click Create Offer to get started."
                      : activeTab === "active"
                      ? "No active offers. Activate an offer or create a new one."
                      : "No inactive offers."}
                  </TableCell>
                </TableRow>
              ) : (
                displayOffers.map((offer) => (
                  <TableRow
                    key={offer.id}
                    className="cursor-pointer hover:bg-muted/50 transition"
                    onClick={() =>
                      router.push(`/dashboard/brand/offers/${offer.id}/analytics`)
                    }
                  >
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{offer.title}</span>
                        {offer.keywords.length > 0 && (
                          <div className="flex mt-1 gap-1">
                            {offer.keywords.slice(0, 2).map((keyword) => (
                              <Badge
                                key={keyword}
                                variant="secondary"
                                className="text-xs px-1"
                              >
                                {keyword}
                              </Badge>
                            ))}
                            {offer.keywords.length > 2 && (
                              <Badge
                                variant="secondary"
                                className="text-xs px-1"
                              >
                                +{offer.keywords.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          offer.status === "Active"
                            ? "bg-green-100 text-green-700 border-green-200"
                            : "bg-gray-100 text-gray-600 border-gray-200"
                        }
                      >
                        {offer.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm font-medium">
                      {offer.reward}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">${offer.offer_total_budget_allocated}</span>
                        <span className="text-xs text-muted-foreground">
                          ${(offer.offer_total_budget_allocated - offer.total_spent_offer).toFixed(2)} remaining
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">${offer.total_spent_offer}</span>
                        <div className="flex gap-1 text-xs">
                          {offer.total_spent_production !== undefined && (
                            <span className="text-green-600">
                              Prod: ${offer.total_spent_production}
                            </span>
                          )}
                          {offer.total_spent_test !== undefined && offer.total_spent_test > 0 && (
                            <span className="text-blue-600">
                              Test: ${offer.total_spent_test}
                            </span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{offer.clicks}</TableCell>
                    <TableCell>{offer.conversions}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span
                          className={
                            Number(offer.ctr) > 0
                              ? "text-green-600"
                              : "text-gray-500"
                          }
                        >
                          {offer.ctr}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {offer.updatedAt}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              `/dashboard/brand/offers/${offer.id}/analytics`
                            );
                          }}
                        >
                          Analytics
                        </Button>
                        {/* <Button
                          variant="outline"
                          size="sm"
                          onClick={async (e) => {
                            e.stopPropagation();
                            setLoadingDetails(true);
                            const offerDetails = await fetchOfferById(offer.id);
                            setDetailedOffer(offerDetails);
                            setLoadingDetails(false);
                            if (offerDetails) {
                              toast.success(`Fetched details for ${offerDetails.title}`);
                              console.log("Offer details:", offerDetails);
                            } else {
                              toast.error(`Failed to fetch details for ${offer.title}`);
                            }
                          }}
                        >
                          Details
                        </Button> */}
                        <Button
                          variant={
                            offer.status === "Active"
                              ? "destructive"
                              : "default"
                          }
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedOffer(offer);

                            setShowActivationDialog(true);
                          }}
                        >
                          {offer.status === "Active" ? "Pause" : "Activate"}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Offer Details Dialog */}
      {detailedOffer && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">{detailedOffer.title} Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDetailedOffer(null)}
              >
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Basic Information</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>ID: <span className="font-mono">{detailedOffer.id}</span></div>
                  <div>Status: <Badge>{detailedOffer.active ? "Active" : "Inactive"}</Badge></div>
                  <div>Product ID: <span className="font-mono">{detailedOffer.product_id}</span></div>
                  <div>Brand ID: <span className="font-mono">{detailedOffer.brand_id}</span></div>
                  <div>Goal: {detailedOffer.goal || "Not specified"}</div>
                  <div>Trust Score: {detailedOffer.trust_score || "50"}</div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Metrics</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Production Clicks: {typeof detailedOffer.click_count === 'object' ?
                    detailedOffer.click_count.production || 0 : detailedOffer.click_count || 0}</div>
                  <div>Test Clicks: {typeof detailedOffer.click_count === 'object' ?
                    detailedOffer.click_count.test || 0 : 0}</div>
                  <div>Production Conversions: {typeof detailedOffer.conversion_count === 'object' ?
                    detailedOffer.conversion_count.production || 0 : detailedOffer.conversion_count || 0}</div>
                  <div>Test Conversions: {typeof detailedOffer.conversion_count === 'object' ?
                    detailedOffer.conversion_count.test || 0 : 0}</div>
                  <div>Budget: ${centsToDollars(detailedOffer.budget || 0)}</div>
                  <div>Total Spent: ${typeof detailedOffer.total_spent === 'object' ?
                    centsToDollars(detailedOffer.total_spent.production || 0) :
                    centsToDollars(detailedOffer.total_spent_offer || 0)}</div>
                </div>
              </div>

              {detailedOffer.tracking && (
                <div>
                  <h3 className="font-semibold mb-2">Tracking</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Method: {detailedOffer.tracking.method || "Not specified"}</div>
                    <div>Redirect URL: {detailedOffer.tracking.redirect_url || "Not specified"}</div>
                    <div>Webhook URL: {detailedOffer.tracking.webhook_url || "Not specified"}</div>
                    <div>Target URLs: {detailedOffer.tracking.target_urls?.length || 0} URLs</div>
                  </div>
                </div>
              )}

              <div>
                <h3 className="font-semibold mb-2">Payout</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Amount: {detailedOffer.payout ?
                    formatCurrency(centsToDollars(detailedOffer.payout.amount),
                    detailedOffer.payout.currency || "USD") : "Not specified"}</div>
                  <div>Model: {detailedOffer.payout?.model || "CPA"}</div>
                  <div>Reward Note: {detailedOffer.reward_note || "Not specified"}</div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Content</h3>
                <div className="text-sm">
                  <div className="mb-2">Description: {detailedOffer.description}</div>
                  <div className="mb-2">URL: <a href={detailedOffer.url} target="_blank" rel="noopener noreferrer"
                    className="text-blue-600 hover:underline">{detailedOffer.url}</a></div>
                  <div className="mb-2">
                    Categories: {detailedOffer.categories.map(cat => (
                      <Badge key={cat} variant="outline" className="mr-1">{cat}</Badge>
                    ))}
                  </div>
                  <div>
                    Keywords: {detailedOffer.keywords.map(keyword => (
                      <Badge key={keyword} variant="secondary" className="mr-1">{keyword}</Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Timestamps</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Created: {detailedOffer.created_at ?
                    format(new Date(detailedOffer.created_at), "MMM dd, yyyy HH:mm") : "Not available"}</div>
                  <div>Last Converted: {detailedOffer.last_converted_at ?
                    format(new Date(detailedOffer.last_converted_at), "MMM dd, yyyy HH:mm") : "Never"}</div>
                  <div>Updated: {detailedOffer.updated_at ?
                    format(new Date(detailedOffer.updated_at), "MMM dd, yyyy HH:mm") : "Not available"}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Offer Activation Dialog */}
      {selectedOffer && (
        <OfferActivationDialog
          isOpen={showActivationDialog}
          onClose={() => {
            setShowActivationDialog(false);
            // Refresh the offers list after dialog closes
            if (user) {
              const fetchOffers = async () => {
                try {
                  const token = await user.getIdToken();
                  const res = await fetch(
                    `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`,
                    {
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  );
                  if (res.ok) {
                    const data = await res.json();
                    if (data.totals) {
                      setTotals(data.totals);
                    }
                    // Log the data to see what we're getting
                    console.log('API response totals:', data.totals);
                    console.log('API response offers:', data.offers);

                    // Check if there are any active offers
                    const activeOffers = data.offers.filter((offer: OfferApiResponse) => offer.active);
                    setHasActiveOffer(activeOffers.length > 0);

                    const results = data.offers.map(
                      (offer: OfferApiResponse) => {
                        // Extract production click count from either the new object structure or fallback to old format
                        // Explicitly use only production clicks, not test clicks or total clicks
                        const clickCount = typeof offer.click_count === 'object'
                          ? (offer.click_count.production || 0)
                          : (offer.clicks || offer.click_count || 0);

                        // Extract production conversion count from either the new object structure or fallback to old format
                        // Explicitly use only production conversions, not test conversions
                        const conversionCount = typeof offer.conversion_count === 'object'
                          ? offer.conversion_count.production || 0
                          : (offer.conversions_production || 0);

                        return {
                          id: offer.id,
                          title: offer.title || "Untitled",
                          status: offer.active ? "Active" : "Inactive",
                          clicks: clickCount,
                          // Only use production conversions
                          conversions: conversionCount,
                          conversions_production: conversionCount,
                          // Budget is stored in cents in the database, convert to dollars for display
                          budget: offer.budget ? centsToDollars(offer.budget) : 0,
                          // Get offer_total_budget_allocated
                          offer_total_budget_allocated: offer.offer_total_budget_allocated ? centsToDollars(offer.offer_total_budget_allocated) : 0,
                          // Only use production spending, convert from cents to dollars
                          total_spent_offer: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent.production || 0) :
                            centsToDollars(offer.total_spent_offer || 0),
                          // Add production and test spending if available, convert from cents to dollars
                          total_spent_production: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent?.production || 0) : undefined,
                          total_spent_test: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent?.test || 0) : undefined,
                          // Include view_count from the API response
                          view_count: offer.view_count || 0,
                          // Calculate CTR using only production conversions and production clicks
                          // If there are no production clicks, CTR is 0
                          ctr: clickCount > 0 && conversionCount >= 0
                            ? ((conversionCount / clickCount) * 100).toFixed(1)
                            : 0,
                          reward:
                            offer.reward_note ||
                            (offer.payout?.amount
                              ? formatCurrency(centsToDollars(offer.payout.amount), offer.payout.currency ?? "USD")
                              : "—"),
                          updatedAt: offer.last_converted_at
                            ? format(new Date(offer.last_converted_at), "MMM dd, yyyy")
                            : "—",
                          description: offer.description || "",
                          url: offer.url || "",
                          keywords: offer.keywords || [],
                          categories: offer.categories || [],
                        };
                      }
                    );
                    setOffers(results);
                  }
                } catch (err) {
                  console.error("Error refreshing offers:", err);
                }
              };
              fetchOffers();
            }
          }}
          offerId={selectedOffer.id}
          offerTitle={selectedOffer.title}
          offerBudget={selectedOffer.offer_total_budget_allocated}
          isActive={selectedOffer.status === "Active"}
        />
      )}
    </div>
  );
}
